# Telegram Bot配置
BOT_TOKEN=your_telegram_bot_token_here
BOT_WEBHOOK_URL=https://yourdomain.com/webhook
# BOT_PROXY=socks5h://127.0.0.1:1080

# 数据库配置
DATABASE_PATH=./database/shop.db

# Web服务配置
PORT=3000
SESSION_SECRET=your_session_secret_here
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# USDT支付配置
TRON_API_KEY=your_tron_api_key
USDT_WALLET_ADDRESS=your_usdt_wallet_address
TRON_NETWORK=mainnet

# 支付宝配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key
ALIPAY_PUBLIC_KEY=your_alipay_public_key
ALIPAY_GATEWAY=https://openapi.alipay.com/gateway.do
ALIPAY_NOTIFY_URL=https://yourdomain.com/api/payment/alipay/notify

# 系统配置
NODE_ENV=development
LOG_LEVEL=info
CARD_EXPIRE_HOURS=24
ORDER_TIMEOUT_MINUTES=30
