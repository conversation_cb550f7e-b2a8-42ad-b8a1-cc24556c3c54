# 卡密批量导入功能测试

## 功能说明
实现了文本输入方式的卡密批量导入功能，支持【卡号#密码】格式，使用系统默认有效期。

## 测试步骤

### 1. 访问管理后台
- 打开浏览器访问：http://localhost:3000/admin/cards
- 使用管理员账号登录（admin/admin123）

### 2. 测试批量导入功能
1. 点击"批量导入"按钮
2. 选择商品
3. 在文本域中输入测试数据：

```
【STEAM001#PASS001】
【STEAM002#PASS002】
【STEAM003#PASS003】
【XBOX001#XPASS001】
【XBOX002#XPASS002】
```

4. 点击"开始导入"按钮

### 3. 验证结果
- 检查是否成功导入5张卡密
- 验证卡密是否使用了系统默认有效期（24小时）
- 检查批次ID是否正确生成

## 功能特性

### 前端功能
- ✅ 批量导入模态框
- ✅ 商品选择下拉框
- ✅ 文本格式验证
- ✅ 实时错误提示
- ✅ 导入进度反馈

### 后端功能
- ✅ 文本格式解析【卡号#密码】
- ✅ 卡号重复检查
- ✅ 系统默认有效期支持
- ✅ 批量创建API适配
- ✅ 错误处理和反馈

### 格式验证
- ✅ 严格的【卡号#密码】格式检查
- ✅ 卡号和密码长度验证（6-50字符）
- ✅ 重复卡号检测
- ✅ 空行自动跳过

## 技术实现

### 前端实现
- 使用Bootstrap模态框
- JavaScript文本解析和验证
- 调用现有的`/api/cards/batch`接口

### 后端实现
- 复用现有的批量创建API
- 集成SystemConfig获取默认有效期
- 使用Card.createBatch()方法

## 测试用例

### 正确格式测试
```
【STEAM001#PASS001】
【STEAM002#PASS002】
【STEAM003#PASS003】
```

### 错误格式测试
```
STEAM001#PASS001  // 缺少【】
【STEAM002PASS002】  // 缺少#分隔符
【#PASS003】  // 卡号为空
【STEAM004#】  // 密码为空
【STEAM005#PASS005】
【STEAM005#PASS006】  // 重复卡号
```

## 预期结果
- 正确格式：成功导入3张卡密
- 错误格式：显示具体错误信息，不执行导入
