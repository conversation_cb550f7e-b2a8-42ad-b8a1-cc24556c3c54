const Product = require('../database/models/Product');
const Card = require('../database/models/Card');
const axios = require('axios');
const logger = require('../utils/logger');

class CardDeliveryService {
  /**
   * 根据商品类型发卡
   * @param {Object} order - 订单信息
   * @param {number} order.product_id - 商品ID
   * @param {number} order.quantity - 数量
   * @param {string} order.id - 订单ID
   * @returns {Promise<Array>} 卡密数组
   */
  static async deliverCards(order) {
    try {
      const product = await Product.findById(order.product_id);
      if (!product) {
        throw new Error('商品不存在');
      }

      logger.info('开始发卡', { 
        orderId: order.id, 
        productId: order.product_id, 
        productType: product.type,
        quantity: order.quantity 
      });

      let cards = [];

      if (product.type === 'card') {
        // 卡密类型：从卡密库获取
        cards = await this.deliverFromCardStock(product, order.quantity);
      } else if (product.type === 'post') {
        // POST类型：调用第三方API
        cards = await this.deliverFromPostAPI(product, order);
      } else {
        throw new Error(`不支持的商品类型: ${product.type}`);
      }

      logger.info('发卡成功', { 
        orderId: order.id, 
        productType: product.type,
        cardCount: cards.length 
      });

      return cards;

    } catch (error) {
      logger.error('发卡失败', { 
        orderId: order.id, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 从卡密库发卡
   * @param {Product} product - 商品对象
   * @param {number} quantity - 数量
   * @returns {Promise<Array>} 卡密数组
   */
  static async deliverFromCardStock(product, quantity) {
    // 获取可用卡密
    const availableCards = await Card.findAll({
      product_id: product.id,
      status: 'available',
      limit: quantity
    });

    if (availableCards.length < quantity) {
      throw new Error(`库存不足，需要${quantity}张卡密，但只有${availableCards.length}张可用`);
    }

    // 标记卡密为已售
    const cardIds = availableCards.slice(0, quantity).map(card => card.id);
    await Card.markAsSold(cardIds);

    // 返回卡密信息
    return availableCards.slice(0, quantity).map(card => ({
      card_number: card.card_number,
      card_password: card.card_password,
      type: 'card'
    }));
  }

  /**
   * 从POST API发卡
   * @param {Product} product - 商品对象
   * @param {Object} order - 订单信息
   * @returns {Promise<Array>} 卡密数组
   */
  static async deliverFromPostAPI(product, order) {
    if (!product.post_data) {
      throw new Error('POST类型商品缺少API配置');
    }

    let postConfig;
    try {
      postConfig = JSON.parse(product.post_data);
    } catch (error) {
      throw new Error('POST配置格式错误，必须是有效的JSON');
    }

    // 验证必要字段
    if (!postConfig.url) {
      throw new Error('POST配置缺少url字段');
    }

    // 替换变量
    const processedConfig = this.processPostVariables(postConfig, {
      product_id: product.id,
      quantity: order.quantity,
      order_id: order.id
    });

    try {
      // 发送POST请求
      const response = await axios({
        method: 'POST',
        url: processedConfig.url,
        headers: processedConfig.headers || {},
        data: processedConfig.body || {},
        timeout: 30000 // 30秒超时
      });

      logger.info('POST API调用成功', { 
        orderId: order.id,
        url: processedConfig.url,
        status: response.status 
      });

      // 处理响应数据
      return this.parsePostResponse(response.data, order.quantity);

    } catch (error) {
      logger.error('POST API调用失败', { 
        orderId: order.id,
        url: processedConfig.url,
        error: error.message 
      });
      
      if (error.response) {
        throw new Error(`API调用失败: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        throw new Error('API请求超时或网络错误');
      } else {
        throw new Error(`API调用错误: ${error.message}`);
      }
    }
  }

  /**
   * 处理POST配置中的变量替换
   * @param {Object} config - POST配置
   * @param {Object} variables - 变量对象
   * @returns {Object} 处理后的配置
   */
  static processPostVariables(config, variables) {
    const configStr = JSON.stringify(config);
    let processedStr = configStr;

    // 替换变量
    Object.keys(variables).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      processedStr = processedStr.replace(regex, variables[key]);
    });

    return JSON.parse(processedStr);
  }

  /**
   * 解析POST API响应
   * @param {*} responseData - API响应数据
   * @param {number} expectedQuantity - 期望的卡密数量
   * @returns {Array} 卡密数组
   */
  static parsePostResponse(responseData, expectedQuantity) {
    // 如果响应是数组，直接使用
    if (Array.isArray(responseData)) {
      if (responseData.length < expectedQuantity) {
        throw new Error(`API返回的卡密数量不足，期望${expectedQuantity}张，实际${responseData.length}张`);
      }
      
      return responseData.slice(0, expectedQuantity).map((item, index) => ({
        card_number: item.card_number || item.cardNumber || item.number || `API-${Date.now()}-${index}`,
        card_password: item.card_password || item.cardPassword || item.password || item.code || '',
        type: 'post'
      }));
    }

    // 如果响应是对象，尝试从data字段获取
    if (responseData && responseData.data && Array.isArray(responseData.data)) {
      return this.parsePostResponse(responseData.data, expectedQuantity);
    }

    // 如果响应是对象且包含cards字段
    if (responseData && responseData.cards && Array.isArray(responseData.cards)) {
      return this.parsePostResponse(responseData.cards, expectedQuantity);
    }

    // 如果是单个卡密对象
    if (responseData && (responseData.card_number || responseData.cardNumber)) {
      if (expectedQuantity > 1) {
        throw new Error(`API只返回了1张卡密，但订单需要${expectedQuantity}张`);
      }
      
      return [{
        card_number: responseData.card_number || responseData.cardNumber || responseData.number,
        card_password: responseData.card_password || responseData.cardPassword || responseData.password || responseData.code || '',
        type: 'post'
      }];
    }

    throw new Error('无法解析API响应数据，请检查API返回格式');
  }
}

module.exports = CardDeliveryService;
